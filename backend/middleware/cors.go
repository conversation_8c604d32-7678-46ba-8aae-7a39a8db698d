package middleware

import (
	"net/http"
	"os"
)

// CORS Middleware
func CORS(next http.Handler) http.Handler {
	allowedOrigin := os.Getenv("FRONTEND_ORIGIN")
	if allowedOrigin == "" {
		allowedOrigin = "http://localhost:8000" // fallback default
	}

	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.<PERSON>er().Set("Access-Control-Allow-Origin", allowedOrigin)
		w.<PERSON><PERSON>().Set("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		w.Header().Set("Access-Control-Allow-Headers", "Content-Type, Authorization")
		w.Header().Set("Access-Control-Allow-Credentials", "true")

		if r.Method == "OPTIONS" {
			w.WriteHeader(http.StatusOK)
			return
		}

		next.ServeHTTP(w, r)
	})
}
