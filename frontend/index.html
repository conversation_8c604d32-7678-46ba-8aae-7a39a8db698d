
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Forum</title>
    <link rel="icon" href="/static/pictures/forum-logo.png" type="image/x-icon"/>
    <link rel="stylesheet" href="/styles/styles.css" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" />
    <style>
        /* Add blur effect styles */
        .main-container {
            transition: filter 0.3s ease-in-out;
        }
        
        .main-container.blur {
            filter: blur(5px) brightness(0.7);
            pointer-events: none;
        }
    </style>
</head>
<body>
    <div class="main-container">
        <!-- Mobile Navigation Elements -->
        <div class="hamburger-menu" aria-label="Toggle sidebar menu">
            <span></span>
            <span></span>
            <span></span>
        </div>

        <div class="mobile-category-dropdown">
            <button class="category-dropdown-btn" aria-label="Category filter">
                <i class="fas fa-tags"></i>
                <span>Categories</span>
                <i class="fas fa-chevron-down"></i>
            </button>
            <div class="category-dropdown-content">
                <div class="category-item active" data-category="all">
                    <i class="fas fa-globe"></i> All Posts
                </div>
                <!-- Additional categories will be loaded dynamically -->
            </div>
        </div>

        <!-- Sidebar Overlay for Mobile -->
        <div class="sidebar-overlay"></div>

        <!-- Navbar -->
        <nav class="navbar" role="navigation" aria-label="Main navigation">
            <div id="navLogoContainer"></div>
            <div class="nav-auth" id="navAuth" role="group" aria-label="User Authentication">

                <!-- Avatar, Username & Logout Button will appear here -->
            </div>
        </nav>
        
        

        <!-- Main Layout -->
        <div class="container">
            <!-- Left Sidebar -->
            <aside class="sidebar left-sidebar" aria-label="Sidebar navigation">
                <div class="profile-section" id="userProfile">
                    <!-- User profile will load dynamically -->
                </div>
                <nav class="menu-section" aria-label="Sidebar menu">
                    <div class="menu-item active" data-view="home"><i class="fas fa-home"></i> Home</div>
                    <div class="menu-item" data-view="profile"><i class="fas fa-user"></i> Profile</div>
                    <div class="menu-item" data-view="trending"><i class="fas fa-fire"></i> Trending</div>
                    <div class="menu-item" data-view="myposts"><i class="fa fa-sticky-note"></i> My Posts</div>
                    <div class="menu-item" data-view="saved"><i class="fas fa-bookmark"></i> Saved Posts</div>
                    <div class="menu-item" data-view="liked"><i class="fas fa-heart"></i> Liked Posts</div>
                </nav>

                <!-- Category Section for Desktop -->
                <section class="category-section" id="categorySection">
                    <h3>Categories</h3>
                    <div id="categoryList">
                        <!-- Categories will be loaded here -->
                    </div>
                </section>
            </aside>

            <!-- Main Content -->
            <main class="main-content" id="mainContent" role="main">
                <section id="storySection" class="story-section">
                    <!-- Stories will be rendered dynamically -->
                </section>
                <section id="createPostSection" class="create-post-section">
                </section>
                
                <section id="postFeed">
                    <!-- Posts will be injected here -->
                </section>
            </main>

            <!-- Right Sidebar -->
            <aside class="sidebar right-sidebar" aria-label="Right sidebar">
                <section id="categoryFilter" class="category-section">
                    <h3>Categories</h3>
                    <!-- Dynamic category filters -->
                </section>
            </aside>
        </div>
    </div>

    <!-- Modals -->
    <!-- Auth Modal -->
    <div id="authModal" class="modal hidden" role="dialog" aria-modal="true">
        <div class="auth-modal-overlay">
            <div class="auth-modal-container">
                <button class="close-btn" aria-label="Close">&times;</button>

                <!-- Tab Navigation -->
                <div class="auth-tabs">
                    <button class="auth-tab active" data-tab="signin">Sign In</button>
                    <button class="auth-tab" data-tab="signup">Sign Up</button>
                </div>

                <!-- Sign In Form -->
                <div class="auth-form-container signin-form active">
                    <div class="auth-form">
                        <div class="form-header">
                            <h2>Welcome Back</h2>
                            <p>Sign in to your account to continue</p>
                        </div>

                        <form class="form-content">
                            <div class="form-group">
                                <label for="signin-email">Email Address</label>
                                <input type="email" id="signin-email" name="email" required />
                            </div>

                            <div class="form-group">
                                <label for="signin-password">Password</label>
                                <input type="password" id="signin-password" name="password" required />
                            </div>

                            <button type="button" class="submit-btn signin-submit">Sign In</button>
                        </form>

                        <div class="form-footer">
                            <p>Don't have an account? <span class="toggle-signup">Sign up here</span></p>
                        </div>
                    </div>
                </div>

                <!-- Sign Up Form -->
                <div class="auth-form-container signup-form">
                    <div class="auth-form">
                        <div class="form-header">
                            <h2>Create Account</h2>
                            <p>Join our community today</p>
                        </div>

                        <form class="form-content">
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="signup-username">Username</label>
                                    <input type="text" id="signup-username" name="username" required />
                                </div>

                                <div class="form-group">
                                    <label for="signup-email">Email Address</label>
                                    <input type="email" id="signup-email" name="email" required />
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="signup-avatar">Profile Picture (Optional)</label>
                                <div class="file-input-wrapper">
                                    <input type="file" id="signup-avatar" name="avatar" accept="image/*" />
                                    <span class="file-input-text">Choose file or drag here</span>
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label for="signup-password">Password</label>
                                    <input type="password" id="signup-password" name="password" required />
                                </div>

                                <div class="form-group">
                                    <label for="signup-confirm">Confirm Password</label>
                                    <input type="password" id="signup-confirm" name="confirmPassword" required />
                                </div>
                            </div>

                            <button type="button" class="submit-btn signup-submit">Create Account</button>
                        </form>

                        <div class="form-footer">
                            <p>Already have an account? <span class="toggle-signin">Sign in here</span></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
  
        <!-- Create Post Modal -->
        <div id="createPostModal" class="modal hidden" role="dialog" aria-modal="true">
            <div class="modal-content">
                <span class="close" role="button" aria-label="Close post modal">&times;</span>
                <div id="postForm"></div>
            </div>
        </div>

        <!-- Profile Modal -->
        <div id="profileModal" class="modal hidden" role="dialog" aria-modal="true">
            <div class="modal-content">
                <span class="close" role="button" aria-label="Close profile modal">&times;</span>
                <div id="profileForm"></div>
            </div>
        </div>
    </div>

    <script type="module" src="/app.mjs"></script>
</body>
</html>
