/**
 * LikedView - Displays posts that the current user has liked
 */

import { PostCard } from '../posts/PostCard.mjs';

export class LikedView {
    constructor(app) {
        this.app = app;
        this.posts = [];
        this.currentPage = 1;
        this.postsPerPage = 10;
        this.isLoading = false;
        this.hasMorePosts = true;
    }

    /**
     * Render the liked posts view
     */
    async render() {
        console.log('LikedView: Rendering liked posts view');
        
        // Check if user is authenticated
        if (!this.app.authManager.isAuthenticated()) {
            console.log('LikedView: User not authenticated, showing login prompt');
            return this.renderLoginPrompt();
        }

        const container = document.createElement('div');
        container.className = 'liked-view';
        container.innerHTML = `
            <div class="view-header">
                <h1><i class="fas fa-heart"></i> Liked Posts</h1>
                <p class="view-description">Posts you've liked</p>
            </div>
            <div class="posts-container" id="likedPostsContainer">
                <div class="loading-spinner" id="loadingSpinner">
                    <i class="fas fa-spinner fa-spin"></i> Loading your liked posts...
                </div>
            </div>
            <div class="load-more-container" id="loadMoreContainer" style="display: none;">
                <button class="load-more-btn" id="loadMoreBtn">
                    <i class="fas fa-plus"></i> Load More Posts
                </button>
            </div>
            <div class="no-posts-message" id="noPostsMessage" style="display: none;">
                <div class="empty-state">
                    <i class="fas fa-heart-broken"></i>
                    <h3>No Liked Posts Yet</h3>
                    <p>You haven't liked any posts yet. Start exploring and like posts that interest you!</p>
                    <button class="explore-btn" onclick="window.app.router.navigate('/')">
                        <i class="fas fa-compass"></i> Explore Posts
                    </button>
                </div>
            </div>
        `;

        // Load initial posts
        await this.loadLikedPosts();

        // Set up event listeners
        this.setupEventListeners(container);

        return container;
    }

    /**
     * Render login prompt for unauthenticated users
     */
    renderLoginPrompt() {
        const container = document.createElement('div');
        container.className = 'liked-view login-required';
        container.innerHTML = `
            <div class="login-prompt">
                <div class="login-prompt-content">
                    <i class="fas fa-heart"></i>
                    <h2>Login Required</h2>
                    <p>You need to be logged in to view your liked posts.</p>
                    <button class="login-btn" id="loginPromptBtn">
                        <i class="fas fa-sign-in-alt"></i> Login
                    </button>
                </div>
            </div>
        `;

        // Add login button event listener
        container.querySelector('#loginPromptBtn').addEventListener('click', () => {
            this.app.authModal.show();
        });

        return container;
    }

    /**
     * Load liked posts from the API
     */
    async loadLikedPosts(page = 1) {
        if (this.isLoading) return;

        console.log(`LikedView: Loading liked posts, page ${page}`);
        this.isLoading = true;

        const loadingSpinner = document.getElementById('loadingSpinner');
        const loadMoreBtn = document.getElementById('loadMoreBtn');
        
        if (loadingSpinner) {
            loadingSpinner.style.display = page === 1 ? 'block' : 'none';
        }
        
        if (loadMoreBtn) {
            loadMoreBtn.disabled = true;
            loadMoreBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Loading...';
        }

        try {
            const response = await fetch(`/api/posts/liked?page=${page}&limit=${this.postsPerPage}`, {
                method: 'GET',
                credentials: 'include',
                headers: {
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                if (response.status === 401) {
                    console.log('LikedView: User not authenticated');
                    this.app.authManager.logout();
                    return;
                }
                throw new Error(`Failed to fetch liked posts: ${response.status}`);
            }

            const likedPosts = await response.json();
            console.log(`LikedView: Received ${likedPosts.length} liked posts`);

            if (page === 1) {
                this.posts = likedPosts;
            } else {
                this.posts = [...this.posts, ...likedPosts];
            }

            this.hasMorePosts = likedPosts.length === this.postsPerPage;
            this.currentPage = page;

            await this.renderPosts();

        } catch (error) {
            console.error('LikedView: Error loading liked posts:', error);
            this.showError('Failed to load liked posts. Please try again.');
        } finally {
            this.isLoading = false;
            
            if (loadingSpinner) {
                loadingSpinner.style.display = 'none';
            }
            
            if (loadMoreBtn) {
                loadMoreBtn.disabled = false;
                loadMoreBtn.innerHTML = '<i class="fas fa-plus"></i> Load More Posts';
            }
        }
    }

    /**
     * Render the posts in the container
     */
    async renderPosts() {
        const container = document.getElementById('likedPostsContainer');
        const noPostsMessage = document.getElementById('noPostsMessage');
        const loadMoreContainer = document.getElementById('loadMoreContainer');

        if (!container) return;

        if (this.posts.length === 0) {
            container.innerHTML = '';
            if (noPostsMessage) noPostsMessage.style.display = 'block';
            if (loadMoreContainer) loadMoreContainer.style.display = 'none';
            return;
        }

        if (noPostsMessage) noPostsMessage.style.display = 'none';

        // Clear container for fresh render
        container.innerHTML = '';

        // Render each post
        for (const post of this.posts) {
            try {
                const postCard = new PostCard(post, this.app);
                const postElement = await postCard.render();
                container.appendChild(postElement);
            } catch (error) {
                console.error('LikedView: Error rendering post:', error);
            }
        }

        // Show/hide load more button
        if (loadMoreContainer) {
            loadMoreContainer.style.display = this.hasMorePosts ? 'block' : 'none';
        }
    }

    /**
     * Set up event listeners
     */
    setupEventListeners(container) {
        // Load more button
        const loadMoreBtn = container.querySelector('#loadMoreBtn');
        if (loadMoreBtn) {
            loadMoreBtn.addEventListener('click', () => {
                if (!this.isLoading && this.hasMorePosts) {
                    this.loadLikedPosts(this.currentPage + 1);
                }
            });
        }
    }

    /**
     * Show error message
     */
    showError(message) {
        const container = document.getElementById('likedPostsContainer');
        if (container) {
            container.innerHTML = `
                <div class="error-message">
                    <i class="fas fa-exclamation-triangle"></i>
                    <p>${message}</p>
                    <button onclick="location.reload()" class="retry-btn">
                        <i class="fas fa-redo"></i> Retry
                    </button>
                </div>
            `;
        }
    }

    /**
     * Refresh the liked posts (called when a post is liked/unliked)
     */
    async refresh() {
        console.log('LikedView: Refreshing liked posts');
        this.currentPage = 1;
        this.hasMorePosts = true;
        await this.loadLikedPosts(1);
    }

    /**
     * Cleanup when view is destroyed
     */
    destroy() {
        // Clean up any event listeners or resources
        this.posts = [];
        this.currentPage = 1;
        this.hasMorePosts = true;
        this.isLoading = false;
    }
}
