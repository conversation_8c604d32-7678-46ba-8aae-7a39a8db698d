/**
 * LikedView - Displays posts that the current user has liked
 */

import { BaseView } from './BaseView.mjs';
import { PostCard } from '../posts/PostCard.mjs';

export class LikedView extends BaseView {
    constructor(app, params, query) {
        console.log('LikedView: Constructor called');
        super(app, params, query);
        this.posts = [];
        this.isLoading = false;
        console.log('LikedView: Constructor completed');
    }

    /**
     * Render the liked posts view
     * @param {HTMLElement} container - Container element
     */
    async render(container) {
        console.log('LikedView: Rendering liked posts view');
        console.log('LikedView: App object:', this.app);
        console.log('LikedView: AuthManager:', this.app.authManager);

        try {
            // Check authentication using BaseView method
            if (!await this.isAuthenticated()) {
                console.log('LikedView: User not authenticated, showing auth modal');
                this.showAuthModal();
                this.app.router.navigate('/', true);
                return;
            }

            console.log('LikedView: User is authenticated, proceeding with render');

            // Clear container
            container.innerHTML = '';

            likedViewContainer.className = 'liked-view';
            likedViewContainer.innerHTML = `
                <div class="view-header">
                    <h1><i class="fas fa-heart"></i> Liked Posts</h1>
                    <p class="view-description">Posts you've liked</p>
                </div>
                <div class="posts-container" id="likedPostsContainer">
                    <div class="loading-spinner" id="loadingSpinner">
                        <i class="fas fa-spinner fa-spin"></i> Loading your liked posts...
                    </div>
                </div>
                <div class="no-posts-message" id="noPostsMessage" style="display: none;">
                    <div class="empty-state">
                        <i class="fas fa-heart-broken"></i>
                        <h3>No Liked Posts Yet</h3>
                        <p>You haven't liked any posts yet. Start exploring and like posts that interest you!</p>
                        <button class="explore-btn" onclick="window.app.router.navigate('/')">
                            <i class="fas fa-compass"></i> Explore Posts
                        </button>
                    </div>
                </div>
            `;

            // Add to container
            container.appendChild(likedViewContainer);

            // Load initial posts
            await this.loadLikedPosts();

            // Set up event listeners
            this.setupEventListeners(likedViewContainer);

        } catch (error) {
            console.error('LikedView: Error rendering view:', error);
            container.innerHTML = `
                <div class="error-message">
                    <i class="fas fa-exclamation-triangle"></i>
                    <p>Failed to load liked posts view. Please try again.</p>
                    <button onclick="location.reload()" class="retry-btn">
                        <i class="fas fa-redo"></i> Retry
                    </button>
                </div>
            `;
        }
    }



    /**
     * Load liked posts by filtering all posts (same pattern as MyPostsView)
     */
    async loadLikedPosts() {
        if (this.isLoading) return;

        console.log('LikedView: Loading liked posts by filtering all posts');
        this.isLoading = true;

        const loadingSpinner = document.getElementById('loadingSpinner');

        if (loadingSpinner) {
            loadingSpinner.style.display = 'block';
        }

        try {
            // Get current user
            const currentUser = await this.getCurrentUser();
            if (!currentUser) {
                throw new Error('User not authenticated');
            }

            console.log('LikedView: Current user:', currentUser.username);

            // Fetch all posts and filter by current user's likes
            const allPosts = await this.app.postManager.fetchForumPosts();
            console.log('LikedView: Total posts fetched:', allPosts.length);

            // Filter posts that the current user has liked
            // We need to check the user's liked posts from their reactions/likes
            const likedPosts = await this.filterLikedPosts(allPosts, currentUser.id);

            console.log('LikedView: Filtered liked posts:', likedPosts.length);

            this.posts = likedPosts;
            await this.renderPosts();

        } catch (error) {
            console.error('LikedView: Error loading liked posts:', error);
            this.showError('Failed to load liked posts. Please try again.');
        } finally {
            this.isLoading = false;

            if (loadingSpinner) {
                loadingSpinner.style.display = 'none';
            }
        }
    }

    /**
     * Filter posts to show only those liked by the current user
     * @param {Array} allPosts - All posts
     * @param {string} userId - Current user ID
     * @returns {Array} - Posts liked by the user
     */
    async filterLikedPosts(allPosts, userId) {
        try {
            // Get user's liked posts by checking reactions for each post
            const likedPosts = [];

            for (const post of allPosts) {
                try {
                    // Check if user has liked this post
                    const reactions = await this.app.reactionManager.getPostReactions(post.id);
                    const userLike = reactions.find(reaction =>
                        reaction.user_id === userId && reaction.type === 'like'
                    );

                    if (userLike) {
                        likedPosts.push(post);
                    }
                } catch (error) {
                    console.warn('LikedView: Error checking reactions for post', post.id, error);
                    // Continue with other posts even if one fails
                }
            }

            return likedPosts;
        } catch (error) {
            console.error('LikedView: Error filtering liked posts:', error);
            return [];
        }
    }

    /**
     * Render the posts in the container
     */
    async renderPosts() {
        const container = document.getElementById('likedPostsContainer');
        const noPostsMessage = document.getElementById('noPostsMessage');

        if (!container) return;

        if (this.posts.length === 0) {
            container.innerHTML = '';
            if (noPostsMessage) noPostsMessage.style.display = 'block';
            return;
        }

        if (noPostsMessage) noPostsMessage.style.display = 'none';

        // Clear container for fresh render
        container.innerHTML = '';

        // Render each post
        for (const post of this.posts) {
            try {
                const postCard = new PostCard(post, this.app);
                const postElement = await postCard.render();
                container.appendChild(postElement);
            } catch (error) {
                console.error('LikedView: Error rendering post:', error);
            }
        }
    }

    /**
     * Set up event listeners
     */
    setupEventListeners(container) {
        // No additional event listeners needed for now
        // Posts will handle their own interactions via PostCard
        console.log('LikedView: Event listeners set up');
    }

    /**
     * Show error message
     */
    showError(message) {
        const container = document.getElementById('likedPostsContainer');
        if (container) {
            container.innerHTML = `
                <div class="error-message">
                    <i class="fas fa-exclamation-triangle"></i>
                    <p>${message}</p>
                    <button onclick="location.reload()" class="retry-btn">
                        <i class="fas fa-redo"></i> Retry
                    </button>
                </div>
            `;
        }
    }

    /**
     * Refresh the liked posts (called when a post is liked/unliked)
     */
    async refresh() {
        console.log('LikedView: Refreshing liked posts');
        await this.loadLikedPosts();
    }

    /**
     * Cleanup when view is destroyed
     */
    destroy() {
        // Clean up any event listeners or resources
        this.posts = [];
        this.isLoading = false;
    }
}
