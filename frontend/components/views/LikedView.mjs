/**
 * Liked View - Shows posts that the user has liked
 */

import { BaseView } from './BaseView.mjs';
import { ApiUtils } from '../utils/ApiUtils.mjs';
import { PostCard } from '../posts/PostCard.mjs';

export class LikedView extends BaseView {
    constructor(app, params, query) {
        super(app, params, query);
        this.currentPage = 1;
        this.postsPerPage = 10;
        this.hasMorePosts = true;
        this.isLoading = false;
    }

    /**
     * Render the liked posts view
     * @param {HTMLElement} container - Container element
     */
    async render(container) {
        console.log('LikedView: Starting render');
        try {
            // Check authentication
            if (!await this.isAuthenticated()) {
                console.log('LikedView: User not authenticated, showing login modal');
                this.showAuthModal();

                // Show a "please log in" message on the page
                container.innerHTML = '';
                const loginPrompt = document.createElement('div');
                loginPrompt.className = 'auth-required-message';
                loginPrompt.innerHTML = `
                    <div class="auth-prompt">
                        <i class="fas fa-heart" style="font-size: 3rem; color: #ccc; margin-bottom: 1rem;"></i>
                        <h2>Login Required</h2>
                        <p>You need to be logged in to view your liked posts.</p>
                        <button class="login-btn" onclick="document.querySelector('.auth-modal-overlay').style.display = 'flex'">
                            <i class="fas fa-sign-in-alt"></i> Login
                        </button>
                        <button class="home-btn" onclick="window.forumApp.router.navigate('/')">
                            <i class="fas fa-home"></i> Go to Home
                        </button>
                    </div>
                `;
                container.appendChild(loginPrompt);
                return;
            }

            console.log('LikedView: User authenticated, rendering content');

            // Clear container
            container.innerHTML = '';

            // Show loading state
            container.appendChild(this.createLoadingElement());

            // Create liked posts view
            await this.renderLikedContent(container);

        } catch (error) {
            console.error('Error rendering liked posts view:', error);
            container.innerHTML = '';
            container.appendChild(this.createErrorElement(
                'Failed to load liked posts.',
                () => this.render(container)
            ));
        }
    }

    /**
     * Render the main liked posts content
     * @param {HTMLElement} container - Container element
     */
    async renderLikedContent(container) {
        // Clear the main content container
        container.innerHTML = '';

        // Hide story section and create post section for this view
        const storySection = document.getElementById('storySection');
        const createPostSection = document.getElementById('createPostSection');
        if (storySection) storySection.style.display = 'none';
        if (createPostSection) createPostSection.style.display = 'none';

        // Create the liked posts header and filters
        const likedHeader = document.createElement('div');
        likedHeader.className = 'view-header liked-header';
        likedHeader.innerHTML = `
            <h1><i class="fas fa-heart"></i> Liked Posts</h1>
            <p>Posts you've liked</p>

            <div class="view-filters">
                <button class="filter-btn active" data-filter="all">All Liked</button>
                <button class="filter-btn" data-filter="recent">Recently Liked</button>
                <button class="filter-btn" data-filter="popular">Most Popular</button>
            </div>
        `;

        // Create the posts container (reuse the existing postFeed structure)
        const postsContainer = document.createElement('section');
        postsContainer.id = 'postFeed';
        postsContainer.className = 'post-feed liked-posts-feed';
        postsContainer.innerHTML = '<div class="loading">Loading your liked posts...</div>';

        // Create pagination controls
        const paginationContainer = document.createElement('div');
        paginationContainer.className = 'pagination-controls';
        paginationContainer.id = 'likedPagination';
        paginationContainer.style.display = 'none';
        paginationContainer.innerHTML = '<button id="loadMoreLiked" class="load-more-btn">Load More</button>';

        // Add everything to the container
        container.appendChild(likedHeader);
        container.appendChild(postsContainer);
        container.appendChild(paginationContainer);

        // Update PostManager's container reference
        this.app.postManager.postContainer = postsContainer;

        // Setup event listeners
        this.setupEventListeners();

        // Load liked posts
        await this.loadLikedPosts();
    }

    /**
     * Setup event listeners
     */
    setupEventListeners() {
        const filterBtns = document.querySelectorAll('.filter-btn');
        
        filterBtns.forEach(btn => {
            btn.addEventListener('click', () => {
                const filter = btn.getAttribute('data-filter');
                
                // Update active state
                filterBtns.forEach(b => b.classList.remove('active'));
                btn.classList.add('active');
                
                // Reset pagination and load filtered data
                this.currentPage = 1;
                this.hasMorePosts = true;
                this.loadLikedPosts(filter);
            });
        });

        // Load more button
        const loadMoreBtn = document.getElementById('loadMoreLiked');
        if (loadMoreBtn) {
            loadMoreBtn.addEventListener('click', () => {
                this.loadMoreLikedPosts();
            });
        }
    }

    /**
     * Load liked posts
     * @param {string} filter - Filter type
     */
    async loadLikedPosts(filter = 'all') {
        const postsContainer = document.getElementById('postFeed');
        if (!postsContainer) return;

        try {
            this.isLoading = true;
            postsContainer.innerHTML = '<div class="loading">Loading your liked posts...</div>';

            // Fetch liked posts from API
            const url = `/api/posts/liked?page=${this.currentPage}&limit=${this.postsPerPage}`;
            const likedPosts = await ApiUtils.get(url, true);

            if (!likedPosts || likedPosts.length === 0) {
                postsContainer.innerHTML = `
                    <div class="empty-state">
                        <i class="fas fa-heart"></i>
                        <h3>No Liked Posts Yet</h3>
                        <p>Start liking posts to see them here!</p>
                        <button class="browse-posts-btn">Browse Posts</button>
                    </div>
                `;

                // Add event listener for browse button
                const browseBtn = postsContainer.querySelector('.browse-posts-btn');
                if (browseBtn) {
                    browseBtn.addEventListener('click', () => {
                        this.app.router.navigate('/');
                    });
                }
                return;
            }

            // Clear container and render posts
            postsContainer.innerHTML = '';

            // Apply filter if needed
            let filteredPosts = likedPosts;
            if (filter === 'recent') {
                // Posts are already sorted by like date (most recent first)
                filteredPosts = likedPosts;
            } else if (filter === 'popular') {
                // Sort by creation date (assuming more recent posts are more popular)
                filteredPosts = [...likedPosts].sort((a, b) => new Date(b.created_at) - new Date(a.created_at));
            }

            // Render each post
            for (const post of filteredPosts) {
                const postCard = PostCard.create(post);
                
                // Setup comment toggle for this post
                PostCard.setupCommentToggle(postCard);
                
                // Setup post navigation for this post
                PostCard.setupPostNavigation(postCard, this.app);
                
                postsContainer.appendChild(postCard);
            }

            // Update pagination
            this.hasMorePosts = likedPosts.length === this.postsPerPage;
            this.updatePaginationControls();

            // Load additional data for posts (likes, comments, etc.)
            await this.loadPostsData();

        } catch (error) {
            console.error('Error loading liked posts:', error);
            const errorInfo = ApiUtils.handleError(error, 'loading liked posts');
            
            if (errorInfo.requiresAuth) {
                this.showAuthModal();
                this.app.router.navigate('/', true);
            } else {
                postsContainer.innerHTML = '';
                postsContainer.appendChild(this.createErrorElement(
                    'Failed to load liked posts.',
                    () => this.loadLikedPosts(filter)
                ));
            }
        } finally {
            this.isLoading = false;
        }
    }

    /**
     * Load more liked posts (pagination)
     */
    async loadMoreLikedPosts() {
        if (this.isLoading || !this.hasMorePosts) return;

        try {
            this.isLoading = true;
            this.currentPage++;

            const url = `/api/posts/liked?page=${this.currentPage}&limit=${this.postsPerPage}`;
            const newPosts = await ApiUtils.get(url, true);

            if (newPosts && newPosts.length > 0) {
                const postsContainer = document.getElementById('postFeed');
                
                // Render new posts
                for (const post of newPosts) {
                    const postCard = PostCard.create(post);
                    PostCard.setupCommentToggle(postCard);
                    PostCard.setupPostNavigation(postCard, this.app);
                    postsContainer.appendChild(postCard);
                }

                // Update pagination state
                this.hasMorePosts = newPosts.length === this.postsPerPage;
                this.updatePaginationControls();

                // Load additional data for new posts
                await this.loadPostsData();
            } else {
                this.hasMorePosts = false;
                this.updatePaginationControls();
            }

        } catch (error) {
            console.error('Error loading more liked posts:', error);
            this.currentPage--; // Revert page increment on error
        } finally {
            this.isLoading = false;
        }
    }

    /**
     * Update pagination controls
     */
    updatePaginationControls() {
        const paginationContainer = document.getElementById('likedPagination');
        const loadMoreBtn = document.getElementById('loadMoreLiked');
        
        if (paginationContainer && loadMoreBtn) {
            if (this.hasMorePosts) {
                paginationContainer.style.display = 'block';
                loadMoreBtn.textContent = this.isLoading ? 'Loading...' : 'Load More';
                loadMoreBtn.disabled = this.isLoading;
            } else {
                paginationContainer.style.display = 'none';
            }
        }
    }

    /**
     * Load additional data for posts (likes, comments, etc.)
     */
    async loadPostsData() {
        if (this.app.reactionManager) {
            await this.app.reactionManager.loadPostsLikes();
            await this.app.reactionManager.loadCommentsLikes();
        }
        
        if (this.app.commentManager) {
            this.app.commentManager.initializeCommentForms();
        }
    }

    /**
     * Cleanup when view is destroyed
     */
    destroy() {
        // Restore the original layout sections
        const storySection = document.getElementById('storySection');
        const createPostSection = document.getElementById('createPostSection');
        if (storySection) storySection.style.display = '';
        if (createPostSection) createPostSection.style.display = '';

        // Call parent destroy
        super.destroy();
    }
}
