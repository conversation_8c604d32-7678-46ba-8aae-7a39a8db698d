/* ===== Variables ===== */
:root {
    --primary-color: white;
    --nav-bar-color: #1d5c7e;
    --accent-color: #1d5c7e;
    --bg-color: #5e95b7;
    --card-color: #f1f7fb;
    --text-color: #1f1f1f;
    --muted-text: #777;
    --border-color: #e0e0e0;
    --hover-color: #1d5c7e;
    --hover-color2: #f1f3f5;
    --radius: 12px;
    --shadow: 0 2px 10px rgba(0,0,0,0.1);
    --transition: 0.25s ease-in-out;
}

/* ===== Reset & Base ===== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}
body {
    font-family: 'Segoe UI', sans-serif;
    background: var(--bg-color);
    color: var(--text-color);
}
a {
    text-decoration: none;
    color: inherit;
}
img {
    max-width: 100%;
    border-radius: var(--radius);
}
.hidden {
    display: none !important;
}
.liked {
    color: limegreen !important;
}

/* ===== Navbar ===== */
.navbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: nowrap;
    background: var(--nav-bar-color);
    padding: 0.8rem 2rem;
    box-shadow: var(--shadow);
    position: sticky;
    top: 0;
    z-index: 999;
    width: 100%;
    gap: 1rem;
    min-height: 60px;
    color: white;
}

/* Logo container */
#navLogoContainer {
    display: flex;
    align-items: center;
    min-width: 60px;
    margin-right: 1rem;
    flex-shrink: 0;
}

.nav-logo {
    width: 60px;
    height: 40px;
    object-fit: contain;
    transition: all 0.3s ease;
}

.clickable-logo {
    cursor: pointer;
    transition: all 0.3s ease;
}

.clickable-logo:hover {
    transform: scale(1.05);
    opacity: 0.8;
    filter: brightness(1.1);
}



/* Auth section */
.nav-auth {
    display: flex;
    align-items: center;
    gap: 10px;
    flex-shrink: 0;
    min-width: fit-content;
    white-space: nowrap;
}

/* Auth buttons (Login/Signup) */
.nav-auth button {
    background: var(--card-color);
    color: var(--nav-bar-color);
    font-weight: 600;
    font-size: 0.9rem;
    padding: 8px 16px;
    border: none;
    border-radius: var(--radius);
    cursor: pointer;
    transition: all 0.3s ease;
    white-space: nowrap;
    min-width: 80px;
}

/* User info when logged in */
.nav-user-info {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 4px;
    border-radius: var(--radius);
    transition: all 0.3s ease;
}

.nav-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    object-fit: cover;
    border: 2px solid var(--primary-color);
    flex-shrink: 0;
}

.clickable-avatar {
    cursor: pointer;
    transition: all 0.3s ease;
}

.clickable-avatar:hover {
    transform: scale(1.05);
    border-color: var(--accent-color);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.nav-username {
    color: var(--primary-color);
    font-weight: 500;
    margin: 0 4px;
    max-width: 150px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.clickable-username {
    cursor: pointer;
    transition: all 0.3s ease;
    padding: 2px 4px;
    border-radius: 4px;
}

.clickable-username:hover {
    background: rgba(255, 255, 255, 0.1);
    color: white;
    transform: translateY(-1px);
}

.logout-btn {
    background: none;
    border: none;
    color: var(--primary-color);
    font-size: 0.9rem;
    cursor: pointer;
    padding: 5px 10px;
    border-radius: var(--radius);
    transition: all 0.3s ease;
    opacity: 0.9;
}

/* ===== Responsive Navbar ===== */

/* Large tablets and small desktops */
@media screen and (max-width: 1200px) {
    .navbar {
        padding: 0.8rem 1.5rem;
        gap: 0.8rem;
    }



    .nav-auth {
        gap: 8px;
    }
}

/* Tablets */
@media screen and (max-width: 1024px) {
    .navbar {
        padding: 0.8rem 1rem;
        gap: 0.6rem;
    }

    .nav-search {
        max-width: 350px;
        min-width: 150px;
    }

    .nav-username {
        max-width: 120px;
    }

    .nav-auth button {
        padding: 6px 12px;
        font-size: 0.9rem;
    }
}

/* Small tablets and large phones */
@media screen and (max-width: 768px) {
    .navbar {
        padding: 0.6rem 0.8rem;
        gap: 0.5rem;
        flex-wrap: nowrap;
        justify-content: space-between;
    }

    /* Reorder navbar elements for mobile: Logo (left) | Auth (right) */
    #navLogoContainer {
        flex-shrink: 0;
    }

    .nav-auth {
        gap: 6px;
        flex-shrink: 0;
        margin-left: auto; /* Push auth to the right */
    }

    .nav-logo {
        width: 45px;
        height: 32px;
    }



    .nav-username {
        max-width: 80px;
        font-size: 0.85rem;
    }

    .nav-auth button {
        padding: 5px 8px;
        font-size: 0.8rem;
    }

    .nav-avatar {
        width: 28px;
        height: 28px;
        cursor: pointer;
    }

    .nav-user-info {
        gap: 6px;
    }
}

/* Mobile phones */
@media screen and (max-width: 480px) {
    .navbar {
        padding: 0.4rem 0.5rem;
        gap: 0.3rem;
        min-height: 50px;
        justify-content: space-between;
    }

    /* Mobile navbar layout: Logo (left) | Auth (right) */
    #navLogoContainer {
        flex-shrink: 0;
    }

    .nav-auth {
        gap: 4px;
        flex-shrink: 0;
        margin-left: auto; /* Keep auth on the right */
    }

    .nav-logo {
        width: 35px;
        height: 28px;
    }



    .nav-username {
        display: none; /* Hide username on very small screens */
    }

    .nav-auth button {
        padding: 4px 6px;
        font-size: 0.75rem;
        min-width: auto;
    }

    .nav-avatar {
        width: 26px;
        height: 26px;
        cursor: pointer;
        border-width: 1px;
    }

    .nav-user-info {
        gap: 4px;
        align-items: center;
    }

    /* Simplify auth buttons on mobile */
    .nav-auth .btn-primary,
    .nav-auth .btn-secondary {
        padding: 4px 6px;
        font-size: 0.75rem;
    }

    .logout-btn {
        padding: 4px 6px;
        font-size: 0.75rem;
    }
}

/* Very small screens */
@media screen and (max-width: 360px) {
    .navbar {
        padding: 0.3rem 0.4rem;
        gap: 0.2rem;
        justify-content: space-between;
    }

    #navLogoContainer {
        flex-shrink: 0;
    }



    .nav-auth {
        flex-shrink: 0;
        margin-left: auto;
    }



    .nav-auth button {
        padding: 3px 5px;
        font-size: 0.7rem;
    }

    .nav-avatar {
        width: 22px;
        height: 22px;
    }

    .nav-logo {
        width: 30px;
        height: 24px;
    }

    .logout-btn {
        padding: 3px 5px;
        font-size: 0.7rem;
    }
}



/* Hover effects */
.nav-auth button:hover {
    background: var(--bg-color);
    color: var(--primary-color);
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
}

.logout-btn:hover {
    background: rgba(255, 255, 255, 0.1);
}

/* ===== Layout ===== */
.container {
    display: grid;
    grid-template-columns: 250px 1fr 250px;
    gap: 2rem;
    max-width: 1600px;
    margin: 0 auto;
    padding: 2rem;
    min-height: calc(100vh - 80px);
}

/* ===== Sidebar ===== */
.sidebar {
    position: sticky;
    top: 80px;
    height: calc(100vh - 80px);
    padding: 1rem;
    background: var(--primary-color);
    border-radius: var(--radius);
    box-shadow: var(--shadow);
    overflow-y: auto;
}

/* Category Section */
.category-section {
    padding: 1rem;
}

.category-section h3 {
    margin-bottom: 1rem;
    font-size: 1.2rem;
    color: var(--text-color);
}

.menu-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 1rem;
    margin-bottom: 0.5rem;
    border-radius: var(--radius);
    cursor: pointer;
    transition: var(--transition);
    color: var(--text-color);
}

.menu-item:hover,
.menu-item.active {
    background: var(--hover-color);
    color: var(--primary-color);
}

.menu-item i {
    width: 20px;
    text-align: center;
}

/* ===== Main Content ===== */
.main-content {
    width: 100%;
    max-width: 800px;
    margin: 0 auto;
}

/* ===== Stories ===== */
.story-section {
    display: flex;
    overflow-x: auto;
    gap: 1rem;
    padding-bottom: 0.5rem;
}
.story-card {
    flex: 0 0 auto;
    width: 120px;
    background: var(--card-color);
    padding: 10px;
    border-radius: var(--radius);
    box-shadow: var(--shadow);
    text-align: center;
}

/* ===== Create Post Section Styling ===== */
.create-post-section {
    background-color: var(--primary-color);
    margin-bottom: 2rem;
}

.create-post-box {
    background: var(--primary-color);
    padding: 1.5rem;
    border-radius: var(--radius);
    box-shadow: var(--shadow);
    display: flex;
    flex-direction: column;
    gap: 1rem;
    border: 1px solid var(--border-color);
    transition: var(--transition);
}

.create-post-box:hover {
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

/* Post Input Container */
.post-input {
    display: flex;
    gap: 0.75rem;
    align-items: stretch;
    width: 100%;
    flex-wrap: nowrap;
}

/* Post Input Field */
#postInput,
.post-input input {
    flex: 1;
    min-width: 0; /* Allows flex item to shrink below content size */
    padding: 8px 12px;
    border: 1px solid var(--border-color);
    border-radius: var(--radius);
    font-size: 0.9rem;
    background: var(--card-color);
    color: var(--text-color);
    outline: none;
    transition: var(--transition);
    resize: vertical;
    min-height: 40px; /* Match button height */
    line-height: 1.4;
    box-sizing: border-box;
}

#postInput:focus,
.post-input input:focus {
    border-color: var(--accent-color);
    box-shadow: 0 0 0 3px rgba(var(--accent-color-rgb), 0.1);
}

/* Post Button */
.post-btn {
    white-space: nowrap;
    padding: 8px 16px;
    min-height: 40px; /* Reduced from 44px */
    font-size: 0.9rem;
    font-weight: 600;
    background: var(--accent-color);
    color: white;
    border: none;
    border-radius: var(--radius);
    cursor: pointer;
    transition: var(--transition);
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    min-width: 80px;
    max-width: 120px;
}

.post-btn:hover {
    background: var(--hover-color);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.post-btn:active {
    transform: translateY(0);
}
/* Placeholder Styling */
#postInput::placeholder,
.post-input input::placeholder {
    color: var(--muted-text);
    font-size: 0.95rem;
}

/* Post Options */
.post-options {
    display: flex;
    justify-content: space-between;
    gap: 0.5rem;
    flex-wrap: wrap;
    padding-top: 0.5rem;
    border-top: 1px solid var(--border-color);
}

.post-option {
    background: none;
    border: none;
    font-size: 0.9rem;
    padding: 0.75rem 1rem;
    cursor: pointer;
    color: var(--muted-text);
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: var(--transition);
    border-radius: var(--radius);
    flex: 1;
    justify-content: center;
    min-height: 44px; /* Touch target */
    white-space: nowrap;
}

.post-option:hover {
    background: var(--hover-color2);
    color: var(--text-color);
    transform: translateY(-1px);
}

.post-option i {
    font-size: 1rem;
}


/* ===== Post Card ===== */
.post-card, .my-post-card {
    background: var(--primary-color);
    border-radius: var(--radius);
    padding: 1rem; 
    box-shadow: 0px 4px 12px rgba(0, 0, 0, 0.15); /* Stronger depth */
    display: flex;
    flex-direction: column;
    gap: 1.25rem;
    margin-bottom: 1rem;
    transition: var(--transition);
    position: relative;
}

.post-card:hover, .my-post-card:hover {
    /* transform: translateY(-2px); */
    box-shadow: 0px 6px 18px rgba(0, 0, 0, 0.2); /* Slightly more lift */
}

/* Clickable post card styling */
.post-card-clickable {
    cursor: pointer;
    transition: all 0.2s ease;
}

.post-card-clickable:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
    border-color: var(--accent-color);
}

.post-card-clickable:active {
    transform: translateY(0);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Prevent text selection on clickable cards */
.post-card-clickable {
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
}

/* Re-enable text selection for specific elements */
.post-card-clickable .post-content,
.post-card-clickable .post-title {
    user-select: text;
    -webkit-user-select: text;
    -moz-user-select: text;
    -ms-user-select: text;
}

/* Post Detail Expansion Styles */
.post-expanded {
    border: 2px solid var(--accent-color);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.post-detail-expansion {
    margin-top: 1rem;
    padding: 1.5rem;
    background: var(--hover-color2);
    border-radius: var(--radius);
    border-top: 1px solid var(--border-color);
}

.post-detail-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    padding-bottom: 0.75rem;
    border-bottom: 1px solid var(--border-color);
}

.collapse-post-btn {
    background: var(--accent-color);
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: var(--radius);
    cursor: pointer;
    font-weight: 500;
    transition: var(--transition);
}

.collapse-post-btn:hover {
    background: var(--primary-color);
}

.post-full-content {
    margin: 1rem 0;
}

.post-image-full {
    width: 100%;
    max-height: 400px;
    object-fit: cover;
    border-radius: var(--radius);
    margin-bottom: 1rem;
    display: block;
}

.post-full-text {
    font-size: 1rem;
    line-height: 1.6;
    color: var(--text-color);
    margin-bottom: 1rem;
}

.post-stats-detailed {
    display: flex;
    gap: 1rem;
    align-items: center;
    padding: 1rem 0;
    border-top: 1px solid var(--border-color);
    border-bottom: 1px solid var(--border-color);
}

.comment-count-detailed {
    color: var(--muted-text);
    font-size: 0.9rem;
}

.comments-section-expanded {
    margin-top: 1.5rem;
}

.comments-header h3 {
    color: var(--text-color);
    margin-bottom: 1rem;
    font-size: 1.2rem;
}

.comment-form-expanded {
    margin-bottom: 1.5rem;
}

.comments-list-expanded {
    max-height: 400px;
    overflow-y: auto;
}

.empty-comments,
.error-comments {
    text-align: center;
    padding: 2rem;
    color: var(--muted-text);
}

.empty-comments i {
    font-size: 2rem;
    margin-bottom: 0.5rem;
    color: var(--accent-color);
}

.auth-prompt {
    text-align: center;
    padding: 1rem;
    background: var(--card-color);
    border-radius: var(--radius);
    border: 1px solid var(--border-color);
}

.login-link {
    background: none;
    border: none;
    color: var(--accent-color);
    text-decoration: underline;
    cursor: pointer;
    font-weight: 500;
}

.login-link:hover {
    color: var(--primary-color);
}

/* Pagination Controls */
.pagination-controls {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 2rem 1rem;
    margin-top: 1rem;
}

.load-more-btn {
    background: var(--accent-color);
    color: white;
    border: none;
    padding: 0.75rem 2rem;
    border-radius: var(--radius);
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 0.5rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.load-more-btn:hover {
    background: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.load-more-btn:active {
    transform: translateY(0);
}

.loading-more {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--muted-text);
    font-size: 1rem;
    padding: 0.75rem 2rem;
}

.loading-more i {
    color: var(--accent-color);
}

.end-of-posts {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--muted-text);
    font-size: 0.9rem;
    padding: 0.75rem 2rem;
    font-style: italic;
}

.end-of-posts i {
    color: var(--accent-color);
}

/* Responsive pagination */
@media screen and (max-width: 768px) {
    .pagination-controls {
        padding: 1.5rem 0.5rem;
    }

    .load-more-btn {
        padding: 0.6rem 1.5rem;
        font-size: 0.9rem;
    }

    .loading-more,
    .end-of-posts {
        font-size: 0.85rem;
        padding: 0.6rem 1.5rem;
    }
}

/* --- Post Header Layout --- */
.post-header {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    position: relative;
}

/* Avatar styling */
.post-author-img {
    width: 50px; 
    height: 50px;
    border-radius: 50%;
    object-fit: cover;
    flex-shrink: 0;
    border: 2px solid var(--border-color);
}

/* Timestamp styling */
.post-time {
    position: absolute;
    top: 0;
    right: 0;
    font-size: 0.8rem;
    color: var(--muted-text);
    padding: 0.25rem 0.5rem;
    border-radius: var(--radius);
    backdrop-filter: blur(2px);
}

/* --- Author Info --- */
.post-author-info {
    display: flex;
    flex-direction: row;  
    align-items: center; 
    gap: 0.75rem;         
}

.post-author-name {
    font-weight: 570; /* Slightly bolder */
    color: var(--text-color);
    font-size: 1.05rem;
}

/* Post content */
.post-content {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.post-title {
    font-size: 1.2rem;
    font-weight: 600;
    line-height: 1.4;
    color: var(--text-color);
    margin-bottom: 0.25rem;
}

/* Post categories styling */
.post-categories {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin: 0.5rem 0;
}

.post-category {
    background: var(--nav-bar-color);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: var(--radius);
    font-size: 0.8rem;
    font-weight: 500;
    white-space: nowrap;
    transition: var(--transition);
}

.post-category:hover {
    background: var(--accent-color);
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Category Dropdown Styling */
.dropdown {
    position: relative;
    width: 100%;
}

.dropdown-toggle {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    padding: 0.75rem 1rem;
    background: var(--primary-color);
    border: 2px solid var(--border-color);
    border-radius: var(--radius);
    cursor: pointer;
    font-size: 0.9rem;
    color: var(--text-color);
    transition: var(--transition);
    min-height: 44px;
    box-sizing: border-box;
}

.dropdown-toggle:hover {
    border-color: var(--accent-color);
    box-shadow: 0 0 0 3px rgba(29, 92, 126, 0.1);
}

.dropdown-toggle:focus {
    outline: none;
    border-color: var(--accent-color);
    box-shadow: 0 0 0 3px rgba(29, 92, 126, 0.2);
}

.dropdown-toggle::after {
    content: '▼';
    font-size: 0.8rem;
    color: var(--muted-text);
    transition: var(--transition);
    margin-left: 0.5rem;
}

.dropdown-toggle.active::after {
    transform: rotate(180deg);
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: var(--primary-color);
    border: 2px solid var(--border-color);
    border-top: none;
    border-radius: 0 0 var(--radius) var(--radius);
    max-height: 200px;
    overflow-y: auto;
    z-index: 1000;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    margin-top: -2px;
}

.dropdown-menu.hidden {
    display: none;
}

.dropdown-menu label {
    display: flex;
    align-items: center;
    padding: 0.75rem 1rem;
    cursor: pointer;
    transition: var(--transition);
    border-bottom: 1px solid var(--hover-color2);
    margin: 0;
    font-size: 0.9rem;
    color: var(--text-color);
}

.dropdown-menu label:last-child {
    border-bottom: none;
}

.dropdown-menu label:hover {
    background: var(--hover-color2);
}

.dropdown-menu input[type="checkbox"] {
    margin-right: 0.75rem;
    width: 16px;
    height: 16px;
    accent-color: var(--accent-color);
    cursor: pointer;
}

.dropdown-menu input[type="checkbox"]:checked + span {
    color: var(--accent-color);
    font-weight: 500;
}

/* Custom scrollbar for dropdown */
.dropdown-menu::-webkit-scrollbar {
    width: 6px;
}

.dropdown-menu::-webkit-scrollbar-track {
    background: var(--hover-color2);
}

.dropdown-menu::-webkit-scrollbar-thumb {
    background: var(--border-color);
    border-radius: 3px;
}

.dropdown-menu::-webkit-scrollbar-thumb:hover {
    background: var(--muted-text);
}


.post-body {
    font-size: 1rem;
    line-height: 1.7;
    color: var(--text-color);
    white-space: pre-wrap;
    word-break: break-word;
}

.post-image {
    display: flex;
    justify-content: center;
    border-radius: var(--radius);
    background: rgba(0, 0, 0, 1);
    min-height: 100px;
    align-items: center;
    overflow: hidden;
    width: 100%;
}

.post-image img {
    width: 100%;
    max-height: 300px;
    object-fit: cover;
    border-radius: var(--radius);
}

/* Image error styling */
.image-error {
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--hover-color2);
    border: 2px dashed var(--border-color);
    border-radius: var(--radius);
    padding: 2rem;
    color: var(--muted-text);
    font-size: 0.9rem;
    font-style: italic;
    min-height: 100px;
    width: 100%;
    box-sizing: border-box;
}


/* Post actions */
.post-actions {
    display: flex;
    gap: 1.5rem;
    padding-top: 1rem;
    border-top: 1px solid var(--border-color);
}

.post-actions button {
    background: none;
    border: none;
    color: var(--muted-text);
    font-size: 0.9rem;
    cursor: pointer;
    padding: 0.5rem 1rem;
    border-radius: var(--radius);
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.post-actions button:hover {
    background: var(--hover-color2);
    color: var(--text-color);
}

.post-actions button i {
    font-size: 1rem;
}

/* ========== comment section =========== */

.post-comment h4 {
    margin: 0.75rem 0.5rem 0.5rem 0.5rem;
    padding: 0.25rem 0.25rem;
    border-bottom: 1px solid var(--border-color);
}

.post-comment .comment {
    margin: 0.5rem 0.5rem;
    padding: 0.5rem 0.25rem;
    border-bottom: 1px solid var(--border-color);
}


.post-comment .comment .comment-time {
    margin-top: 0.3rem;
    font-size: small;
    color: var(--muted-text);
    text-align: right;
}

/* Compact styling for reply comments */
.comment.reply-comment .comment-time {
    font-size: 0.7rem;
    margin-top: 0.2rem;
}

.comment.reply-comment .comment-username {
    font-size: 0.85rem;
    font-weight: 600;
}

.comment.reply-comment .comment-text {
    font-size: 0.9rem;
    line-height: 1.3;
}

.comment-actions {
    display: flex;
    gap: 4rem;
}

.comment-actions button {
    width: 1rem;
    background: none;
    border: none;
    color: var(--muted-text);
    font-size: small;
    cursor: pointer;
    padding: 0.5rem 0.5rem;
    border-radius: var(--radius);
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.comment-footer {
    padding-top: 0.3rem ;
    display: flex;
    justify-content: space-between;
}

.comment .comment-wrapper {
    display: flex;
    flex-direction: row;

    gap: 0.7rem;
}

.comment {
    display: flex;
    flex-direction: column;

    gap: 0.7rem;
}

.comment-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    object-fit: cover;
    flex-shrink: 0;
    margin-top: -10px;
    display: block;
}
 .comment-details {
    flex: 1;
    width: auto;
 }

.comment-box-form {
    display: flex;
    justify-content: space-evenly;
    align-items: center;
 }

 .reply-comment-header {
    margin: 0.5rem 1.5rem;
    padding: 0.2rem 0.3rem;
    display: flex;
    justify-content: space-between;
 }
 button.close-reply {
    width: 4rem;
    background: none;
    border: 1px solid var(--border-color);
    color: var(--muted-text);
    font-size: small;
    cursor: pointer;
    padding: 0.3rem 0.3rem;
    border-radius: var(--radius);
 }
  button.close-reply:hover {
    background: var(--hover-color2);
    cursor: pointer;
    transition: var(--transition);

  }

 .comment-box-form button {
    background: none;
    color: var(--bg-color); 
    border: 1px solid var(--border-color);
    border-radius: var(--radius);
    width: 6rem;

 }

 .comment-box-form button:hover {
    background: var(--hover-color);
    color: var(--primary-color);
    border: 1px solid var(--border-color);
    cursor: pointer;
    border-radius: var(--radius);
    transition: var(--transition);
 }

 .comment-box-form textarea, .comment-box-form button {
    height: 40px;
    padding: 0.6rem;
    margin: 0.3rem 0.3rem;
 }

.comment-box-form textarea {
    resize: none;
    overflow: hidden;
    padding: 0.6rem;
    overflow-wrap: break-word;    
    border-radius: var(--radius);
    border: 1px solid var(--border-color);
    flex: 1;
 }

 .comment-box-form textarea:actifocusve {
    outline:none;
 }


 .comments-container, .reply-comments-container {
    max-height: 45dvh;
    min-height: 40dvh;
    overflow: scroll;
    margin-top: 0.5rem;
 }

 /* Reduce spacing between comment form and comments */
 .write-comment-box {
    margin-bottom: 0.5rem;
 }

 .post-comment {
    padding-top: 0.5rem;
 }

/* ========== Threaded Comments Styles =========== */

/* Reply comment styling - clean thread approach */
.comment.reply-comment {
    margin-left: 2.5rem;
    margin-top: 0.75rem;
    margin-bottom: 0.5rem;
    padding: 0.5rem 0 0.5rem 1rem;
    background: none !important;
    border: none !important;
    border-left: 2px solid var(--border-color);
    border-radius: 0 !important;
    position: relative;
}

/* Thread line connector */
.comment.reply-comment::before {
    content: '';
    position: absolute;
    left: -2px;
    top: -0.5rem;
    width: 1rem;
    height: 0.5rem;
    border-left: 2px solid var(--border-color);
    border-bottom: 2px solid var(--border-color);
    border-bottom-left-radius: 6px;
}

/* Smaller avatar for replies */
.comment.reply-comment .comment-avatar img {
    width: 32px;
    height: 32px;
}

.comment.reply-comment .comment-avatar {
    margin-top: 0;
    margin-right: 0;
    width: 35px;
    height: 35px;
}

/* Replies container - positioned directly under parent comment */
.replies-container {
    margin-top: 0.5rem;
    margin-bottom: 0;
}

/* Compact comment structure for replies */
.comment.reply-comment .comment-content {
    margin-bottom: 0.25rem;
    font-size: 0.9rem;
}

.comment.reply-comment .comment-footer {
    margin-top: 0.25rem;
}

/* No reactions for reply comments - they should not have comment-actions */
.comment.reply-comment .comment-actions {
    display: none;
}

/* Reply form styling - cleaner approach */
.reply-comment-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
    padding: 6px 12px;
    background-color: #f8f9fa;
    border-radius: 4px;
    border-left: 3px solid #007bff;
}

.reply-comment-header p {
    margin: 0;
    font-style: italic;
    color: #495057;
    font-size: 13px;
}

.close-reply {
    background: #dc3545;
    color: white;
    border: none;
    padding: 3px 8px;
    border-radius: 3px;
    cursor: pointer;
    font-size: 11px;
    font-weight: 500;
}

.close-reply:hover {
    background: #c82333;
}

/* Original comment in reply form - minimal styling */
.original-comment {
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    margin-bottom: 8px;
    padding: 8px;
}

.original-comment .comment-actions {
    display: none;
}

.original-comment .comment-footer {
    margin-top: 4px;
}

/* Reduce overall comment spacing for better density */
.comment {
    margin-bottom: 12px;
    padding: 8px;
}

/* Make thread lines more subtle on hover */
.comment.reply-comment:hover {
    border-left-color: #007bff;
    background-color: #f8f9fa;
}

.comment.reply-comment:hover::before {
    border-color: #007bff;
}

 .reply-comments-container {
    margin-left: 1rem;
 }
 
 .reply-comments-container .post-author-img {
    width: 40px;
    height: 40px;
    object-fit: cover;
    margin-top: 5px;
 }

.reply-comment-header p {
    color: var(--bg-color);
}

 .close-reply {
    cursor: pointer;
 }


/* ===== Modals ===== */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.6);
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
    z-index: 9999;
    display: flex;
    justify-content: center;
    align-items: center;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.modal.hidden {
    opacity: 0;
    visibility: hidden;
    pointer-events: none;
}

.modal:not(.hidden) {
    opacity: 1;
    visibility: visible;
    pointer-events: all;
}

/* Blur effect for main content when modal is open */
.main-container.blur {
    filter: blur(5px);
    transition: filter 0.3s ease;
    pointer-events: none;
}

.main-container {
    transition: filter 0.3s ease;
}

.modal-content {
    background: transparent;
    position: relative;
    width: 900px;
    max-width: 95%;
    padding: 0;
    border-radius: 15px;
}

.close {
    position: absolute;
    right: 10px;
    top: 10px;
    color: white;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    z-index: 1001;
}

/* ===== Auth Modal Styles ===== */
.auth-modal-overlay {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 20px;
    box-sizing: border-box;
}

.auth-modal-container {
    background: white;
    border-radius: 16px;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.25);
    width: 100%;
    max-width: 500px;
    max-height: 90vh;
    overflow: hidden;
    position: relative;
    z-index: 10000;
    animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-30px) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* Ensure modal appears above everything */
.modal * {
    box-sizing: border-box;
}

/* Prevent body scroll when modal is open */
body.modal-open {
    overflow: hidden;
}

.close-btn {
    position: absolute;
    top: 16px;
    right: 16px;
    background: none;
    border: none;
    font-size: 24px;
    color: #666;
    cursor: pointer;
    z-index: 10001;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.2s ease;
}

.close-btn:hover {
    background: #f5f5f5;
    color: #333;
}

/* Auth Tabs */
.auth-tabs {
    display: flex;
    border-bottom: 1px solid #e5e5e5;
}

.auth-tab {
    flex: 1;
    padding: 16px 24px;
    background: none;
    border: none;
    font-size: 16px;
    font-weight: 500;
    color: #666;
    cursor: pointer;
    transition: all 0.2s ease;
    border-bottom: 2px solid transparent;
}

.auth-tab.active {
    color: var(--bg-color);
    border-bottom-color: var(--bg-color);
}

.auth-tab:hover {
    color: var(--bg-color);
    background: #f8f9fa;
}

/* Form Containers */
.auth-form-container {
    display: none;
    padding: 32px;
    max-height: 70vh;
    overflow-y: auto;
}

.auth-form-container.active {
    display: block;
}

.auth-form {
    max-width: 400px;
    margin: 0 auto;
}

/* Form Header */
.form-header {
    text-align: center;
    margin-bottom: 32px;
}

.form-header h2 {
    font-size: 28px;
    font-weight: 600;
    color: #333;
    margin-bottom: 8px;
}

.form-header p {
    color: #666;
    font-size: 16px;
    margin: 0;
}

/* Form Content */
.form-content {
    margin-bottom: 24px;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 16px;
    margin-bottom: 16px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    font-weight: 500;
    color: #333;
    margin-bottom: 8px;
    font-size: 14px;
}

.form-group input {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #e5e5e5;
    border-radius: 8px;
    font-size: 16px;
    transition: border-color 0.2s ease;
    background: #fff;
}

.form-group input:focus {
    outline: none;
    border-color: var(--bg-color);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-group input:invalid {
    border-color: #ef4444;
}

/* File Input Styling */
.file-input-wrapper {
    position: relative;
    border: 2px dashed #e5e5e5;
    border-radius: 8px;
    padding: 24px;
    text-align: center;
    transition: border-color 0.2s ease;
    cursor: pointer;
}

.file-input-wrapper:hover {
    border-color: var(--bg-color);
}

.file-input-wrapper input[type="file"] {
    position: absolute;
    opacity: 0;
    width: 100%;
    height: 100%;
    cursor: pointer;
    border: none;
    padding: 0;
}

.file-input-text {
    color: #666;
    font-size: 14px;
}

/* Submit Button */
.submit-btn {
    width: 100%;
    padding: 14px 24px;
    background: var(--bg-color);
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
}

.submit-btn:hover {
    background: var(--hover-color);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.submit-btn:active {
    transform: translateY(0);
}

/* Form Footer */
.form-footer {
    text-align: center;
    margin-top: 24px;
}

.form-footer p {
    color: #666;
    font-size: 14px;
    margin: 0;
}

.toggle-signup,
.toggle-signin {
    color: var(--bg-color);
    cursor: pointer;
    font-weight: 500;
    text-decoration: underline;
}

.toggle-signup:hover,
.toggle-signin:hover {
    color: var(--hover-color);
}

/* ===== Responsive ===== */
@media (max-width: 991px) {
    .container {
        grid-template-columns: 200px 1fr;
    }
    .right-sidebar {
        display: none;
    }
}
@media (max-width: 767px) {
    .container {
        grid-template-columns: 1fr;
    }
    .sidebar {
        display: none;
    }
    .navbar {
        flex-direction: row;
        align-items: flex-start;
    }
    .nav-search {
        width: 100%;
        margin-top: 0.5rem;
    }
}
/* ===== Post Form Responsive Design ===== */

/* Large tablets and small desktops */
@media screen and (max-width: 1200px) {
    .create-post-box {
        padding: 1.25rem;
    }

    .post-input {
        gap: 0.8rem;
    }

    .post-btn {
        padding: 8px 14px;
        font-size: 0.85rem;
        min-width: 70px;
        max-width: 100px;
    }
}

/* Tablets */
@media screen and (max-width: 1024px) {
    .create-post-box {
        padding: 1rem;
    }

    .post-input {
        gap: 0.75rem;
    }

    #postInput,
    .post-input input {
        padding: 10px 14px;
        font-size: 0.95rem;
    }

    .post-btn {
        padding: 6px 12px;
        font-size: 0.8rem;
        min-width: 60px;
        max-width: 90px;
    }

    .post-options {
        gap: 0.25rem;
    }

    .post-option {
        padding: 0.6rem 0.8rem;
        font-size: 0.85rem;
    }
}

/* Small tablets and large phones */
@media screen and (max-width: 768px) {
    .create-post-box {
        padding: 0.8rem;
        margin-bottom: 1.5rem;
    }

    .post-input {
        gap: 0.5rem;
        flex-wrap: nowrap; /* Keep horizontal on tablets */
    }

    #postInput,
    .post-input input {
        padding: 8px 12px;
        font-size: 0.9rem;
        min-height: 40px;
    }

    .post-btn {
        padding: 8px 16px;
        font-size: 0.85rem;
        min-width: 80px;
    }

    .post-options {
        flex-wrap: wrap;
        gap: 0.25rem;
    }

    .post-option {
        padding: 0.5rem 0.75rem;
        font-size: 0.8rem;
        flex: 1 1 calc(50% - 0.125rem); /* Two per row */
        min-width: 120px;
    }
}

/* Mobile phones */
@media screen and (max-width: 600px) {
    .create-post-box {
        padding: 0.75rem;
        margin-bottom: 1rem;
    }

    .post-input {
        flex-direction: column;
        align-items: stretch;
        gap: 0.75rem;
    }

    #postInput,
    .post-input input {
        width: 100%;
        padding: 10px 12px;
        font-size: 0.9rem;
        min-height: 44px; /* Restore touch target for mobile */
    }

    .post-btn {
        width: 100%;
        padding: 12px;
        font-size: 0.9rem;
        justify-content: center;
    }

    .post-options {
        flex-direction: column;
        gap: 0.5rem;
    }

    .post-option {
        width: 100%;
        padding: 0.75rem;
        font-size: 0.85rem;
        justify-content: flex-start;
        flex: none;
    }
}

/* Small mobile phones */
@media screen and (max-width: 480px) {
    .create-post-box {
        padding: 0.5rem;
        border-radius: 8px;
    }

    #postInput,
    .post-input input {
        padding: 8px 10px;
        font-size: 0.85rem;
    }

    .post-btn {
        padding: 10px;
        font-size: 0.85rem;
    }

    .post-option {
        padding: 0.6rem;
        font-size: 0.8rem;
    }

    .post-option i {
        font-size: 0.9rem;
    }
}

/* Very small screens */
@media screen and (max-width: 360px) {
    .create-post-box {
        padding: 0.4rem;
        gap: 0.75rem;
    }

    #postInput,
    .post-input input {
        padding: 6px 8px;
        font-size: 0.8rem;
    }

    .post-btn {
        padding: 8px;
        font-size: 0.8rem;
    }

    .post-option {
        padding: 0.5rem;
        font-size: 0.75rem;
        gap: 0.25rem;
    }
}

/* Auth Modal Responsive Design */
@media (max-width: 768px) {
    .auth-modal-container {
        max-width: 95%;
        margin: 10px;
    }

    .auth-form-container {
        padding: 24px 20px;
    }

    .form-row {
        grid-template-columns: 1fr;
        gap: 0;
    }

    .form-header h2 {
        font-size: 24px;
    }

    .auth-tab {
        padding: 12px 16px;
        font-size: 14px;
    }
}

@media (max-width: 480px) {
    .auth-modal-container {
        max-width: 100%;
        margin: 0;
        border-radius: 0;
        max-height: 100vh;
    }

    .auth-form-container {
        padding: 20px 16px;
        max-height: calc(100vh - 60px);
    }
}





/* Post Feed Section */
#postFeed {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
    width: 100%;
    max-width: 800px;
    margin: 0 auto;
    transition: opacity 0.3s ease;
}

/* ===== Page Layout Responsive Design ===== */

/* Large screens */
@media screen and (max-width: 1400px) {
    .container {
        grid-template-columns: 220px 1fr 220px;
        gap: 1.5rem;
        padding: 1.5rem;
    }
}

/* Medium screens */
@media screen and (max-width: 1200px) {
    .container {
        grid-template-columns: 200px 1fr 200px;
        gap: 1.25rem;
        padding: 1.25rem;
    }

    .sidebar {
        padding: 0.75rem;
    }

    .main-content {
        max-width: none;
    }
}

/* Small laptops and large tablets */
@media screen and (max-width: 1024px) {
    .container {
        grid-template-columns: 180px 1fr 180px;
        gap: 1rem;
        padding: 1rem;
    }

    .sidebar {
        padding: 0.5rem;
        font-size: 0.9rem;
    }

    .menu-item {
        padding: 0.5rem 0.75rem;
        font-size: 0.85rem;
    }

    .menu-item i {
        width: 16px;
        font-size: 0.9rem;
    }
}

/* Tablets */
@media screen and (max-width: 992px) {
    .container {
        grid-template-columns: 1fr;
        gap: 1rem;
        padding: 1rem 0.5rem;
    }

    .sidebar {
        display: none;
    }

    .main-content {
        width: 100%;
        max-width: 700px;
        margin: 0 auto;
    }

    #postFeed {
        max-width: 100%;
    }
}

/* Large mobile phones */
@media screen and (max-width: 768px) {
    .container {
        padding: 0.75rem 0.25rem;
        gap: 0.75rem;
    }

    .main-content {
        padding: 0 0.5rem;
        max-width: 100%;
    }

    #postFeed {
        max-width: 100%;
    }

    /* Hide sidebars completely on mobile */
    .left-sidebar,
    .right-sidebar {
        display: none !important;
    }
}

/* Mobile phones */
@media screen and (max-width: 600px) {
    .container {
        padding: 0.5rem 0;
        gap: 0.5rem;
        min-height: calc(100vh - 60px);
    }

    .main-content {
        padding: 0 0.25rem;
        max-width: 100%;
    }

    /* Ensure full width on mobile */
    .post-card,
    .create-post-box,
    .trending-view,
    .profile-view,
    .category-view {
        margin: 0 0.5rem;
        border-radius: 8px;
    }

    #postFeed {
        max-width: 100%;
    }
}

/* Small mobile phones */
@media screen and (max-width: 480px) {
    .container {
        padding: 0.25rem 0;
        gap: 0.25rem;
    }

    .main-content {
        padding: 0;
    }

    .post-card,
    .create-post-box {
        margin: 0 0.25rem;
        border-radius: 6px;
    }
}

/* Very small screens */
@media screen and (max-width: 360px) {
    .container {
        padding: 0.125rem 0;
    }

    .post-card,
    .create-post-box {
        margin: 0 0.125rem;
        border-radius: 4px;
    }
}

/* ===== Post Card Responsive Design ===== */

/* Tablets and smaller */
@media screen and (max-width: 768px) {
    .post-card {
        padding: 0.75rem;
        margin-bottom: 0.75rem;
    }

    .post-header {
        gap: 0.75rem;
    }

    .post-author-img {
        width: 40px;
        height: 40px;
    }

    .post-author-name {
        font-size: 1rem;
    }

    .post-title {
        font-size: 1.1rem;
        line-height: 1.3;
    }

    .post-body {
        font-size: 0.95rem;
        line-height: 1.6;
    }

    .post-actions {
        gap: 1rem;
        padding-top: 0.75rem;
    }

    .post-actions button {
        padding: 0.4rem 0.8rem;
        font-size: 0.85rem;
    }
}

/* Mobile phones */
@media screen and (max-width: 600px) {
    .post-card {
        padding: 0.5rem;
        margin-bottom: 0.5rem;
    }

    .post-header {
        gap: 0.5rem;
        flex-wrap: wrap;
    }

    .post-author-img {
        width: 35px;
        height: 35px;
    }

    .post-author-name {
        font-size: 0.9rem;
    }

    .post-time {
        position: static;
        font-size: 0.75rem;
        padding: 0;
        width: 100%;
        text-align: left;
        margin-top: 0.25rem;
    }

    .post-title {
        font-size: 1rem;
        margin-bottom: 0.5rem;
    }

    .post-body {
        font-size: 0.9rem;
        line-height: 1.5;
    }

    .post-actions {
        gap: 0.5rem;
        padding-top: 0.5rem;
        flex-wrap: wrap;
    }

    .post-actions button {
        padding: 0.3rem 0.6rem;
        font-size: 0.8rem;
        flex: 1;
        min-width: 80px;
    }
}

/* Small mobile phones */
@media screen and (max-width: 480px) {
    .post-card {
        padding: 0.4rem;
    }

    .post-author-img {
        width: 30px;
        height: 30px;
    }

    .post-author-name {
        font-size: 0.85rem;
    }

    .post-title {
        font-size: 0.95rem;
    }

    .post-body {
        font-size: 0.85rem;
    }

    .post-actions button {
        padding: 0.25rem 0.5rem;
        font-size: 0.75rem;
    }
}

/* Menu Section Styling */
.menu-section {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    padding: 1rem;
}

.menu-section .menu-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 1rem;
    border-radius: var(--radius);
    cursor: pointer;
    transition: var(--transition);
    color: var(--text-color);
    font-weight: 500;
}

.menu-section .menu-item:hover,
.menu-section .menu-item.active {
    background: var(--hover-color);
    color: var(--primary-color);
    transform: translateX(5px);
}

.menu-section .menu-item i {
    width: 20px;
    text-align: center;
    font-size: 1.1rem;
}

/* ===== Router Views ===== */
.loading {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 2rem;
    color: var(--muted-text);
}

.loading-spinner {
    text-align: center;
}

.loading-spinner i {
    font-size: 2rem;
    margin-bottom: 1rem;
    color: var(--accent-color);
}

.error-message {
    background: var(--primary-color);
    border: 1px solid #ff6b6b;
    border-radius: var(--radius);
    padding: 2rem;
    text-align: center;
    margin: 1rem 0;
}

.error-content h3 {
    color: #ff6b6b;
    margin-bottom: 1rem;
}

.error-content i {
    font-size: 2rem;
    color: #ff6b6b;
    margin-bottom: 1rem;
}

.retry-btn {
    background: var(--accent-color);
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: var(--radius);
    cursor: pointer;
    margin-top: 1rem;
}

.empty-state {
    background: var(--primary-color);
    border-radius: var(--radius);
    padding: 3rem 2rem;
    text-align: center;
    margin: 1rem 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 1rem;
}

.empty-content i {
    font-size: 3rem;
    color: var(--muted-text);
    margin-bottom: 1rem;
}

.empty-content h3 {
    color: var(--text-color);
    margin-bottom: 0.5rem;
}

.empty-content p {
    color: var(--muted-text);
}

/* Profile View */
.profile-view {
    background: var(--primary-color);
    border-radius: var(--radius);
    box-shadow: var(--shadow);
    overflow: hidden;
}

.profile-header {
    display: flex;
    align-items: center;
    gap: 2rem;
    padding: 2rem;
    background: linear-gradient(135deg, var(--nav-bar-color), var(--bg-color));
    color: white;
}

/* ===== Profile View Responsive Design ===== */

/* Tablets */
@media screen and (max-width: 768px) {
    .profile-header {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
        padding: 1.5rem;
    }

    .profile-avatar .avatar-large {
        width: 100px;
        height: 100px;
    }

    .profile-details {
        padding: 1.5rem;
    }

    .profile-info-section {
        padding: 1.5rem;
    }

    .profile-field .field-value {
        font-size: 1rem;
        padding: 0.6rem;
    }
}

/* Mobile phones */
@media screen and (max-width: 600px) {
    .profile-header {
        padding: 1rem;
        gap: 0.75rem;
    }

    .profile-avatar .avatar-large {
        width: 80px;
        height: 80px;
        border-width: 2px;
    }

    .profile-info h1 {
        font-size: 1.5rem;
    }

    .profile-details {
        padding: 1rem;
    }

    .profile-info-section {
        padding: 1rem;
    }

    .profile-info-section h3 {
        font-size: 1.1rem;
        margin-bottom: 1rem;
    }

    .profile-data {
        gap: 1rem;
    }

    .profile-field .field-value {
        font-size: 0.95rem;
        padding: 0.5rem;
    }
}

/* Small mobile phones */
@media screen and (max-width: 480px) {
    .profile-header {
        padding: 0.75rem;
    }

    .profile-avatar .avatar-large {
        width: 70px;
        height: 70px;
    }

    .profile-info h1 {
        font-size: 1.3rem;
    }

    .profile-email,
    .profile-joined {
        font-size: 0.85rem;
    }

    .profile-details {
        padding: 0.75rem;
    }

    .profile-info-section {
        padding: 0.75rem;
    }
}

.profile-avatar .avatar-large {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    border: 4px solid white;
    object-fit: cover;
}

.profile-info h1 {
    margin-bottom: 0.5rem;
}

.profile-email {
    opacity: 0.9;
    margin-bottom: 0.5rem;
}

.profile-joined {
    opacity: 0.8;
    font-size: 0.9rem;
    margin-bottom: 1rem;
}

.edit-profile-btn {
    background: white;
    color: var(--nav-bar-color);
    border: none;
    padding: 0.5rem 1rem;
    border-radius: var(--radius);
    cursor: pointer;
    font-weight: 500;
}

.profile-tabs {
    display: flex;
    border-bottom: 1px solid var(--border-color);
}

.tab-btn {
    background: none;
    border: none;
    padding: 1rem 2rem;
    cursor: pointer;
    border-bottom: 2px solid transparent;
    transition: var(--transition);
}

.tab-btn.active {
    border-bottom-color: var(--accent-color);
    color: var(--accent-color);
}

.tab-content {
    display: none;
    padding: 2rem;
}

.tab-content.active {
    display: block;
}

/* Profile Details Section */
.profile-details {
    padding: 2rem;
}

.profile-info-section {
    background: var(--card-color);
    border-radius: var(--radius);
    padding: 2rem;
    border: 1px solid var(--border-color);
}

.profile-info-section h3 {
    color: var(--text-color);
    margin-bottom: 1.5rem;
    font-size: 1.3rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.profile-info-section h3 i {
    color: var(--accent-color);
}

.profile-data {
    display: grid;
    gap: 1.5rem;
}

.profile-field {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.profile-field label {
    font-weight: 600;
    color: var(--muted-text);
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.profile-field .field-value {
    color: var(--text-color);
    font-size: 1.1rem;
    padding: 0.75rem;
    background: var(--primary-color);
    border-radius: var(--radius);
    border: 1px solid var(--border-color);
}

/* Trending View */
.trending-view {
    background: var(--primary-color);
    border-radius: var(--radius);
    box-shadow: var(--shadow);
    overflow: hidden;
}

.trending-header {
    padding: 2rem;
    background: #1d5c7e;
    color: white;
    text-align: center;
}

/* ===== Trending View Responsive Design ===== */

/* Tablets */
@media screen and (max-width: 768px) {
    .trending-header {
        padding: 1.5rem;
    }

    .trending-header h1 {
        font-size: 1.8rem;
    }

    .trending-filters {
        flex-wrap: wrap;
        gap: 0.5rem;
        padding: 0.75rem;
    }

    .filter-btn {
        padding: 0.4rem 0.8rem;
        font-size: 0.85rem;
    }

    .trending-section {
        padding: 1.5rem;
    }

    .trending-post-item {
        flex-direction: column;
        gap: 0.75rem;
        padding: 0.75rem;
    }

    .trending-rank {
        align-self: flex-start;
        min-width: auto;
    }

    .rank-number {
        font-size: 1.2rem;
    }

    .post-header {
        gap: 0.75rem;
    }

    .post-avatar {
        width: 35px;
        height: 35px;
    }

    .post-title {
        font-size: 1rem;
    }

    .trending-topic-item {
        padding: 0.6rem;
    }

    .topic-rank {
        font-size: 0.8rem;
        min-width: 25px;
    }
}

/* Mobile phones */
@media screen and (max-width: 600px) {
    .trending-header {
        padding: 1rem;
    }

    .trending-header h1 {
        font-size: 1.5rem;
    }

    .trending-filters {
        padding: 0.5rem;
        gap: 0.25rem;
    }

    .filter-btn {
        padding: 0.3rem 0.6rem;
        font-size: 0.8rem;
        flex: 1;
        min-width: 80px;
    }

    .trending-section {
        padding: 1rem;
    }

    .trending-section h2 {
        font-size: 1.2rem;
        margin-bottom: 0.75rem;
    }

    .trending-post-item {
        padding: 0.5rem;
        margin-bottom: 0.75rem;
    }

    .rank-number {
        font-size: 1rem;
    }

    .post-avatar {
        width: 30px;
        height: 30px;
    }

    .post-title {
        font-size: 0.95rem;
        line-height: 1.3;
    }

    .post-author,
    .post-date {
        font-size: 0.8rem;
    }

    .post-snippet {
        font-size: 0.85rem;
        line-height: 1.3;
    }

    .engagement-stats {
        gap: 0.5rem;
        flex-wrap: wrap;
    }

    .stat,
    .engagement-score {
        font-size: 0.8rem;
    }

    .trending-topic-item {
        padding: 0.5rem;
    }

    .topic-name {
        font-size: 0.9rem;
    }

    .topic-count {
        font-size: 0.75rem;
    }
}

/* Small mobile phones */
@media screen and (max-width: 480px) {
    .trending-header {
        padding: 0.75rem;
    }

    .trending-header h1 {
        font-size: 1.3rem;
    }

    .trending-section {
        padding: 0.75rem;
    }

    .trending-post-item {
        padding: 0.4rem;
    }

    .post-title {
        font-size: 0.9rem;
    }

    .post-snippet {
        font-size: 0.8rem;
    }

    .trending-topic-item {
        padding: 0.4rem;
    }
}

.trending-header h1 {
    margin-bottom: 0.5rem;
}

.trending-filters {
    display: flex;
    justify-content: center;
    gap: 1rem;
    padding: 1rem;
    border-bottom: 1px solid var(--border-color);
}

.filter-btn {
    background: none;
    border: 1px solid var(--border-color);
    padding: 0.5rem 1rem;
    border-radius: var(--radius);
    cursor: pointer;
    transition: var(--transition);
}

.filter-btn.active {
    background: var(--accent-color);
    color: white;
    border-color: var(--accent-color);
}

.trending-section {
    padding: 2rem;
    border-bottom: 1px solid var(--border-color);
}

.trending-section:last-child {
    border-bottom: none;
}

.trending-section h2 {
    margin-bottom: 1rem;
    color: var(--text-color);
}

.trending-post-item {
    display: flex;
    gap: 1rem;
    padding: 1rem;
    border-radius: var(--radius);
    cursor: pointer;
    transition: var(--transition);
    margin-bottom: 1rem;
    background: var(--card-color);
    border: 1px solid var(--border-color);
}

.trending-post-item:hover {
    background: var(--hover-color2);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.trending-rank {
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 40px;
}

.rank-number {
    font-size: 1.5rem;
    font-weight: bold;
    color: var(--accent-color);
}

.post-content {
    flex: 1;
}

.post-header {
    display: flex;
    gap: 1rem;
    margin-bottom: 0.5rem;
}

.post-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    object-fit: cover;
}

.post-meta {
    flex: 1;
}

.post-title {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 0.25rem;
    color: var(--text-color);
    line-height: 1.3;
}

.post-author {
    font-size: 0.9rem;
    color: var(--muted-text);
    margin-bottom: 0.25rem;
}

.post-date {
    font-size: 0.8rem;
    color: var(--muted-text);
}

.post-snippet {
    font-size: 0.9rem;
    color: var(--text-color);
    line-height: 1.4;
    margin-bottom: 1rem;
}

.engagement-stats {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.stat {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    font-size: 0.9rem;
    color: var(--muted-text);
}

.engagement-score {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    font-size: 0.9rem;
    color: var(--accent-color);
    font-weight: 600;
    margin-left: auto;
}

.post-image-container {
    margin: 1rem 0;
    border-radius: var(--radius);
    overflow: hidden;
    min-height: 100px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.post-image-container .image-error {
    margin: 0;
    width: 100%;
    min-height: 150px;
}

.trending-post-image {
    width: 100%;
    max-height: 200px;
    object-fit: cover;
    display: block;
    border-radius: var(--radius);
}

.trending-topic-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 0.75rem;
    border-radius: var(--radius);
    cursor: pointer;
    transition: var(--transition);
    margin-bottom: 0.5rem;
    background: var(--card-color);
    border: 1px solid var(--border-color);
}

.trending-topic-item:hover {
    background: var(--hover-color2);
    transform: translateX(5px);
}

.topic-rank {
    font-size: 0.9rem;
    font-weight: bold;
    color: var(--accent-color);
    min-width: 30px;
}

.topic-info {
    flex: 1;
}

.topic-name {
    font-weight: 600;
    color: var(--text-color);
    display: block;
    margin-bottom: 0.25rem;
}

.topic-count {
    font-size: 0.8rem;
    color: var(--muted-text);
}

.topic-arrow {
    color: var(--muted-text);
    font-size: 0.8rem;
}

/* Post Detail View */
.post-detail-view {
    background: var(--primary-color);
    border-radius: var(--radius);
    box-shadow: var(--shadow);
    overflow: hidden;
}

.post-detail-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 2rem;
    border-bottom: 1px solid var(--border-color);
}

.back-btn {
    background: none;
    border: none;
    color: var(--text-color);
    cursor: pointer;
    padding: 0.5rem;
    border-radius: var(--radius);
    transition: var(--transition);
}

.back-btn:hover {
    background: var(--hover-color2);
}

.post-actions {
    display: flex;
    gap: 1rem;
}

.save-post-btn,
.share-post-btn {
    background: none;
    border: none;
    color: var(--muted-text);
    cursor: pointer;
    padding: 0.5rem;
    border-radius: var(--radius);
    transition: var(--transition);
}

.save-post-btn:hover,
.share-post-btn:hover {
    background: var(--hover-color2);
    color: var(--text-color);
}

.save-post-btn.saved {
    color: var(--accent-color);
}

.post-detail-content {
    padding: 2rem;
}

.post-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
}

.post-author {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.author-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    object-fit: cover;
}

.author-info {
    display: flex;
    flex-direction: column;
}

.author-name {
    font-weight: 600;
    color: var(--text-color);
}

.post-date {
    font-size: 0.9rem;
    color: var(--muted-text);
}

.post-category {
    background: var(--accent-color);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: var(--radius);
    font-size: 0.8rem;
    font-weight: 500;
    margin-right: 0.5rem;
    margin-bottom: 0.25rem;
    display: inline-block;
}

.post-categories {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.post-title {
    font-size: 2rem;
    margin-bottom: 1rem;
    color: var(--text-color);
}

/* Post detail view specific image styling */
.post-detail-view .post-image {
    width: 100%;
    max-height: 400px;
    object-fit: cover;
    border-radius: var(--radius);
    margin-bottom: 1rem;
}

.post-content {
    font-size: 1.1rem;
    line-height: 1.6;
    color: var(--text-color);
    margin-bottom: 2rem;
}

.post-footer {
    border-top: 1px solid var(--border-color);
    padding-top: 1rem;
}

.like-btn {
    background: none;
    border: none;
    color: var(--muted-text);
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem;
    border-radius: var(--radius);
    transition: var(--transition);
}

.like-btn:hover {
    background: var(--hover-color2);
}

.like-btn.liked {
    color: #ff6b6b;
}

.comment-count {
    color: var(--muted-text);
    margin-left: 1rem;
}

/* Comments Section */
.comments-section {
    border-top: 1px solid var(--border-color);
    padding: 2rem;
}

.comments-header h2 {
    margin-bottom: 1rem;
    color: var(--text-color);
}

.comment-form-content {
    margin-bottom: 2rem;
}

.comment-input {
    width: 100%;
    padding: 1rem;
    border: 1px solid var(--border-color);
    border-radius: var(--radius);
    resize: vertical;
    font-family: inherit;
    font-size: 1rem;
    background: var(--primary-color);
    color: var(--text-color);
}

.comment-form-actions {
    margin-top: 1rem;
    display: flex;
    justify-content: flex-end;
}

.submit-comment-btn {
    background: var(--accent-color);
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: var(--radius);
    cursor: pointer;
    font-weight: 500;
    transition: var(--transition);
}

.submit-comment-btn:hover {
    background: var(--hover-color);
}

/* Comments List Styling */
.comments-list h4 {
    color: var(--text-color);
    margin-bottom: 1rem;
    font-size: 1.2rem;
}

/* Top-level comments */
.comments-list .comment {
    margin-bottom: 1rem;
    padding: 1rem;
    background: var(--card-color);
    border-radius: var(--radius);
    border: 1px solid var(--border-color);
    display: flex;
    gap: 0.75rem;
}

/* Reply comments - no background, indented */
.comments-list .reply-comment {
    margin-left: 2.5rem;
    margin-top: 0.75rem;
    margin-bottom: 0.5rem;
    padding: 0.5rem 0;
    background: none;
    border: none;
    border-left: 2px solid var(--border-color);
    padding-left: 1rem;
    border-radius: 0;
}

/* Comment Thread Container - each comment and its replies */
.comment-thread {
    margin-bottom: 1rem;
    border-bottom: 1px solid var(--border-color);
    padding-bottom: 0.75rem;
}

.comment-thread:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

/* Replies container */
.replies-container {
    margin-top: 0.5rem;
}

/* Reply Form Container - positioned under specific comments */
.reply-form-container {
    margin-top: 0.75rem;
    margin-bottom: 0.75rem;
    padding: 1rem;
    background: var(--hover-color2);
    border-radius: var(--radius);
    border-left: 3px solid var(--accent-color);
}

/* Reply comment header */
.reply-comment-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.75rem;
}

.reply-comment-header p {
    margin: 0;
    font-style: italic;
    color: var(--accent-color);
    font-weight: 500;
}

.reply-comment-header .close-reply {
    background: none;
    border: 1px solid var(--border-color);
    color: var(--muted-text);
    padding: 0.25rem 0.75rem;
    border-radius: var(--radius);
    cursor: pointer;
    font-size: 0.85rem;
    transition: var(--transition);
}

.reply-comment-header .close-reply:hover {
    background: var(--hover-color);
    color: var(--text-color);
}

/* Original comment preview in reply form */
.original-comment-preview {
    background: var(--card-color);
    border-radius: var(--radius);
    padding: 0.75rem;
    margin-bottom: 0.75rem;
    border: 1px solid var(--border-color);
    opacity: 0.8;
}

.original-comment-preview .comment-avatar img {
    width: 28px;
    height: 28px;
}

.original-comment-preview .comment-details {
    font-size: 0.9rem;
}

/* Reply form styling */
.reply-form {
    margin-top: 0.5rem;
}

.reply-form textarea {
    border: 2px solid var(--accent-color);
    border-radius: var(--radius);
    padding: 0.75rem;
    font-size: 0.95rem;
}

.reply-form textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
}

.reply-form button {
    background: var(--accent-color);
    color: white;
    border: none;
    padding: 0.5rem 1.5rem;
    border-radius: var(--radius);
    font-weight: 500;
    margin-top: 0.5rem;
    cursor: pointer;
    transition: var(--transition);
}

.reply-form button:hover {
    background: var(--primary-color);
}

.comments-list .comment-avatar img {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    object-fit: cover;
}

.comments-list .comment-details {
    flex: 1;
}

.comments-list .comment-content {
    margin-bottom: 0.5rem;
    line-height: 1.4;
}

.comments-list .comment-username {
    color: var(--accent-color);
    font-weight: 600;
}

.comments-list .comment-text {
    color: var(--text-color);
}

.comments-list .comment-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.comments-list .comment-actions {
    display: flex;
    gap: 0.5rem;
}

.comments-list .reaction-btn {
    background: none;
    border: none;
    color: var(--muted-text);
    cursor: pointer;
    padding: 0.25rem 0.5rem;
    border-radius: var(--radius);
    transition: var(--transition);
    font-size: 0.9rem;
}

.comments-list .reaction-btn:hover {
    background: var(--hover-color2);
    color: var(--text-color);
}

.comments-list .comment-time {
    color: var(--muted-text);
    font-size: 0.8rem;
}

.auth-prompt {
    text-align: center;
    padding: 2rem;
    color: var(--muted-text);
}

.login-link {
    background: none;
    border: none;
    color: var(--accent-color);
    cursor: pointer;
    text-decoration: underline;
}

/* Category View */
.category-view {
    background: var(--primary-color);
    border-radius: var(--radius);
    box-shadow: var(--shadow);
    overflow: hidden;
}

.category-header {
    display: block;
    flex-direction: column;
    align-items: center;
    padding: 2rem;
    background:#1d5c7e;
    color: white;
}

.category-info {
    text-align:  center;
}


.category-info h1 {
    margin-bottom: 0.5rem;
    font-size: 2rem;
    text-align: center;
}

.category-info p {
    opacity: 0.9;
    font-size: 1.1rem;
    text-align: center;
}



.category-filters {
    display: flex;
    justify-content: center;
    gap: 1rem;
    padding: 1rem;
    border-bottom: 1px solid var(--border-color);
}

.category-content {
    padding: 2rem;
}

.category-posts {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.category-post-card {
    border: 1px solid var(--border-color);
    border-radius: var(--radius);
    transition: var(--transition);
}

.category-post-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

/* Saved View */
.saved-view {
    background: var(--primary-color);
    border-radius: var(--radius);
    box-shadow: var(--shadow);
    overflow: hidden;
}

.my-posts-header {
    padding: 2rem;
    background: #1d5c7e;
    color: white;
    text-align: center;
}

.my-posts-header h1 {
    margin-bottom: 0.5rem;
}

.my-posts-filters {
    display: flex;
    justify-content: center;
    gap: 1rem;
    padding: 1rem;
    border-bottom: 1px solid var(--border-color);
}

.my-posts-content {
    padding: 2rem;
}

.created-posts {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}


/* 404 Not Found View */
.not-found-view {
    background: var(--primary-color);
    border-radius: var(--radius);
    box-shadow: var(--shadow);
    overflow: hidden;
    min-height: 600px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.not-found-container {
    text-align: center;
    padding: 3rem 2rem;
    max-width: 600px;
    width: 100%;
}

.not-found-icon {
    font-size: 4rem;
    color: var(--accent-color);
    margin-bottom: 2rem;
}

.not-found-title {
    font-size: 6rem;
    font-weight: bold;
    color: var(--accent-color);
    margin-bottom: 1rem;
    line-height: 1;
}

.not-found-subtitle {
    font-size: 2rem;
    color: var(--text-color);
    margin-bottom: 1rem;
}

.not-found-message {
    font-size: 1.1rem;
    color: var(--muted-text);
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

.not-found-path {
    background: var(--card-color);
    padding: 1rem;
    border-radius: var(--radius);
    margin-bottom: 2rem;
    border: 1px solid var(--border-color);
}

.not-found-path code {
    background: var(--bg-color);
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-family: 'Courier New', monospace;
    color: var(--accent-color);
}

.not-found-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    margin-bottom: 3rem;
    flex-wrap: wrap;
}

.btn-primary, .btn-secondary {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: var(--radius);
    cursor: pointer;
    font-weight: 600;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    transition: var(--transition);
}

.btn-primary {
    background: var(--accent-color);
    color: white;
}

.btn-primary:hover {
    background: var(--hover-color);
    transform: translateY(-2px);
}

.btn-secondary {
    background: var(--card-color);
    color: var(--text-color);
    border: 1px solid var(--border-color);
}

.btn-secondary:hover {
    background: var(--hover-color2);
    transform: translateY(-2px);
}

.not-found-suggestions {
    margin-bottom: 2rem;
}

.not-found-suggestions h3 {
    color: var(--text-color);
    margin-bottom: 1rem;
    font-size: 1.2rem;
}

.suggestion-links {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-top: 1rem;
}

.suggestion-link {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 1rem;
    background: var(--card-color);
    border: 1px solid var(--border-color);
    border-radius: var(--radius);
    text-decoration: none;
    color: var(--text-color);
    transition: var(--transition);
}

.suggestion-link:hover {
    background: var(--hover-color2);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.suggestion-link i {
    color: var(--accent-color);
    font-size: 1.2rem;
    width: 20px;
    text-align: center;
}

.suggestion-link span {
    font-weight: 500;
}

.not-found-help {
    border-top: 1px solid var(--border-color);
    padding-top: 2rem;
}

.help-text {
    color: var(--muted-text);
    font-size: 0.9rem;
    line-height: 1.5;
}

/* Responsive design for 404 page */
@media (max-width: 768px) {
    .not-found-title {
        font-size: 4rem;
    }

    .not-found-subtitle {
        font-size: 1.5rem;
    }

    .not-found-actions {
        flex-direction: column;
        align-items: center;
    }

    .btn-primary, .btn-secondary {
        width: 100%;
        max-width: 250px;
        justify-content: center;
    }

    .suggestion-links {
        grid-template-columns: 1fr;
    }
}

/* ===== HAMBURGER MENU AND MOBILE NAVIGATION ===== */

/* Hamburger Menu Styles */
.hamburger-menu {
    display: none;
    flex-direction: column;
    cursor: pointer;
    padding: 0.5rem;
    background: var(--card-color);
    border: 1px solid var(--border-color);
    border-radius: var(--radius);
    position: fixed;
    top: 70px;
    left: 1rem;
    z-index: 1001;
    width: 40px;
    height: 40px;
    justify-content: center;
    align-items: center;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: var(--transition);
    pointer-events: auto;
    user-select: none;
}

.hamburger-menu:hover {
    background: var(--hover-color2);
    transform: scale(1.05);
}

.hamburger-menu span {
    width: 20px;
    height: 2px;
    background: var(--text-color);
    margin: 2px 0;
    transition: all 0.3s ease;
    transform-origin: center;
    display: block;
}

.hamburger-menu.active span:nth-child(1) {
    transform: rotate(45deg) translate(5px, 5px);
}

.hamburger-menu.active span:nth-child(2) {
    opacity: 0;
}

.hamburger-menu.active span:nth-child(3) {
    transform: rotate(-45deg) translate(7px, -6px);
}

/* Mobile Category Dropdown */
.mobile-category-dropdown {
    display: none;
    position: fixed;
    top: 70px;
    right: 1rem;
    z-index: 1001;
}

.category-dropdown-btn {
    background: var(--card-color);
    border: 1px solid var(--border-color);
    border-radius: var(--radius);
    padding: 0.5rem 1rem;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--text-color);
    font-size: 0.9rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: var(--transition);
}

.category-dropdown-btn:hover {
    background: var(--hover-color2);
    transform: scale(1.05);
}

.category-dropdown-btn.active {
    background: var(--accent-color);
    color: white;
}

.category-dropdown-content {
    position: absolute;
    top: 100%;
    right: 0;
    background: var(--card-color);
    border: 1px solid var(--border-color);
    border-radius: var(--radius);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    min-width: 200px;
    max-height: 300px;
    overflow-y: auto;
    display: none;
    margin-top: 0.5rem;
}

.category-dropdown-content.show {
    display: block;
}

.category-dropdown-content .category-item {
    padding: 0.75rem 1rem;
    cursor: pointer;
    border-bottom: 1px solid var(--border-color);
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.category-dropdown-content .category-item:last-child {
    border-bottom: none;
}

.category-dropdown-content .category-item:hover {
    background: var(--hover-color2);
}

.category-dropdown-content .category-item.active {
    background: var(--accent-color);
    color: white;
}

/* Mobile Sidebar Overlay */
.sidebar-overlay {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 999;
    backdrop-filter: blur(2px);
}

.sidebar-overlay.show {
    display: block;
}

/* Mobile responsive adjustments */
@media (max-width: 768px) {
    .hamburger-menu {
        display: flex;
    }

    .mobile-category-dropdown {
        display: block;
    }

    .container {
        grid-template-columns: 1fr;
        grid-template-areas: "main";
        gap: 0;
        padding: 5rem 0.5rem 0.5rem;
    }

    .left-sidebar {
        display: block !important; /* Override any display: none rules */
        position: fixed;
        top: 60px;
        left: -280px;
        width: 280px;
        height: calc(100vh - 60px);
        z-index: 1000;
        transition: left 0.3s ease;
        background: var(--primary-color);
        border-right: 1px solid var(--border-color);
        overflow-y: auto;
        padding: 1rem;
        box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
    }

    /* Ensure sidebar content is visible on mobile */
    .left-sidebar .menu-section {
        display: flex !important;
        flex-direction: column;
        gap: 0.5rem;
        margin-bottom: 1rem;
    }

    .left-sidebar .profile-section {
        display: block !important;
        padding: 1rem;
        border-bottom: 1px solid var(--border-color);
        margin-bottom: 1rem;
    }

    .left-sidebar .category-section {
        display: block !important;
        padding: 0;
        margin-top: 1rem;
    }

    .left-sidebar .menu-item {
        display: flex !important;
        align-items: center;
        gap: 0.75rem;
        padding: 0.75rem 1rem;
        border-radius: var(--radius);
        cursor: pointer;
        transition: var(--transition);
        color: var(--text-color) !important;
        font-weight: 500;
        background: transparent;
        border: none;
        text-align: left;
        width: 100%;
        margin-bottom: 0.25rem;
        font-size: 1rem;
        line-height: 1.5;
        min-height: 44px;
        box-sizing: border-box;
    }

    .left-sidebar .menu-item:hover,
    .left-sidebar .menu-item.active {
        background: var(--hover-color);
        color: var(--primary-color);
        transform: translateX(5px);
    }

    .left-sidebar .menu-item i {
        width: 20px;
        text-align: center;
        font-size: 1.1rem;
    }

    .left-sidebar .category-section h3 {
        color: var(--text-color);
        margin-bottom: 1rem;
        font-size: 1.1rem;
        padding: 0 1rem;
    }

    .left-sidebar.show {
        left: 0;
    }

    .right-sidebar {
        display: none;
    }

    .main-content {
        grid-area: main;
        width: 100%;
        max-width: 100%;
        margin: 0;
    }

    .navbar {
        padding: 0.5rem 1rem;
        position: sticky;
        top: 0;
        z-index: 1100;
    }
}

/* Additional mobile adjustments for smaller screens */
@media (max-width: 480px) {
    .hamburger-menu {
        top: 60px;
        left: 0.5rem;
        width: 35px;
        height: 35px;
    }

    .mobile-category-dropdown {
        top: 60px;
        right: 0.5rem;
    }

    .category-dropdown-btn {
        padding: 0.4rem 0.8rem;
        font-size: 0.8rem;
    }

    .container {
        padding: 4.5rem 0.25rem 0.25rem;
    }

    .left-sidebar {
        width: 260px;
        left: -260px;
        top: 50px;
        height: calc(100vh - 50px);
    }
}

@media (max-width: 360px) {
    .hamburger-menu {
        top: 55px;
        left: 0.25rem;
        width: 32px;
        height: 32px;
    }

    .mobile-category-dropdown {
        top: 55px;
        right: 0.25rem;
    }

    .container {
        padding: 4rem 0.125rem 0.125rem;
    }

    .left-sidebar {
        width: 240px;
        left: -240px;
        top: 45px;
        height: calc(100vh - 45px);
    }
}
